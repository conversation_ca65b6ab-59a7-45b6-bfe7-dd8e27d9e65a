{% extends 'recipes/base.html' %}
{% load static %}

{% block title %}Recipe Finder - Discover{% endblock %}

{% block extra_css %}
<!-- All CSS is now consolidated in base.css, components.css, and pages.css -->
{% endblock %}

{% block content %}
<div class="discover-container" x-data="discoverPage()">
    <div class="discover-header">
        <h1>Discover Recipes</h1>
    </div>

    {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
    {% endif %}

    <!-- Featured Random Recipe Section -->
    <div id="featured-recipe-container">
        {% if featured_recipe %}
            {% include 'recipes/partials/featured_recipe.html' %}
        {% else %}
            <div class="featured-recipe">
                <div class="featured-recipe-image">
                    <span>🍽️ Loading Recipe...</span>
                </div>
                <div class="featured-recipe-info">
                    <div class="featured-recipe-title">Loading...</div>
                    <div class="featured-recipe-details">Please wait while we find you a delicious recipe</div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Randomize Button -->
    <button
        class="randomize-btn htmx-indicator"
        hx-get="{% url 'recipes:discover_random' %}"
        hx-target="#featured-recipe-container"
        hx-trigger="click"
        hx-indicator=".randomize-btn"
        @click="randomizeRecipe()">
        🎲 Try Another Random Recipe
    </button>

    <!-- Categories Strip -->
    <div class="categories-strip">
        <div class="category-chip active"
             @click="loadCategory('all')"
             :class="{ 'active': activeCategory === 'all' }">
            All
        </div>
        {% for category in categories %}
            <div class="category-chip"
                 hx-get="{% url 'recipes:discover_category' category=category %}"
                 hx-target="#recipes-container"
                 hx-trigger="click"
                 @click="loadCategory('{{ category }}')"
                 :class="{ 'active': activeCategory === '{{ category }}' }">
                {{ category }}
            </div>
        {% endfor %}
    </div>

    <!-- Recipes Grid Container -->
    <div id="recipes-container" class="recipes-grid">
        {% if category_recipes %}
            <!-- Display initial mixed recipes from multiple categories -->
            {% for category, recipes in category_recipes.items %}
                {% for recipe in recipes|slice:":2" %}
                    <a href="{% url 'recipes:recipe_detail_api' recipe_id=recipe.id %}" class="recipe-card">
                        <div class="recipe-image">
                            {% if recipe.image %}
                                <img src="{{ recipe.image }}" alt="{{ recipe.title }}" loading="lazy">
                            {% else %}
                                <span>🍽️ {{ recipe.title|truncatechars:20 }}</span>
                            {% endif %}
                        </div>
                        <div class="recipe-info">
                            <div class="recipe-title">{{ recipe.title|truncatechars:30 }}</div>
                            <div class="recipe-area">{{ recipe.area|default:"International" }}</div>
                            <div class="recipe-category">{{ recipe.category|default:"Main Course" }}</div>
                        </div>
                    </a>
                {% endfor %}
            {% endfor %}
        {% else %}
            <div class="loading">
                Loading delicious recipes...
            </div>
        {% endif %}
    </div>

    <!-- Loading indicator for HTMX requests -->
    <div id="loading" class="loading htmx-indicator" style="display: none;">
        Loading delicious recipes...
    </div>
</div>

<script>
    function discoverPage() {
        return {
            activeCategory: 'all',

            loadCategory(category) {
                this.activeCategory = category;

                // If "All" is selected, reload the mixed recipes
                if (category === 'all') {
                    // Reload the page to show mixed recipes
                    window.location.reload();
                }
            },

            randomizeRecipe() {
                // This is handled by HTMX, but we can add any additional logic here
                console.log('Loading new random recipe...');
            }
        }
    }

    // Initialize HTMX indicators
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading states for better UX
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            if (evt.target.classList.contains('randomize-btn')) {
                evt.target.disabled = true;
            }
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.target.classList.contains('randomize-btn')) {
                evt.target.disabled = false;
            }
        });
    });
</script>

<!-- Search history is included in base.html -->
{% endblock %}