/**
 * Search History Manager for Recipe Finder
 * Handles local storage of search queries, filters, and timestamps
 */

class SearchHistoryManager {
    constructor() {
        this.storageKey = 'recipe_finder_search_history';
        this.maxHistoryItems = 50; // Maximum number of search history items to store
        this.maxDisplayItems = 10; // Maximum number of items to display in suggestions
        this.init();
    }

    init() {
        console.log('🔍 Search History Manager initialized');
        this.bindEvents();
        this.cleanupOldEntries();
    }

    /**
     * Save a search query to local storage
     * @param {string} query - The search query
     * @param {string} searchType - Type of search (ingredients, name, cuisine, etc.)
     * @param {Object} filters - Applied filters
     * @param {number} resultCount - Number of results found
     */
    saveSearch(query, searchType, filters = {}, resultCount = 0) {
        if (!query || query.trim().length < 2) {
            return; // Don't save very short queries
        }

        try {
            const searchEntry = {
                id: this.generateId(),
                query: query.trim(),
                searchType: searchType,
                filters: filters,
                resultCount: resultCount,
                timestamp: Date.now(),
                date: new Date().toISOString()
            };

            let history = this.getSearchHistory();
            
            // Remove duplicate entries (same query and search type)
            history = history.filter(item => 
                !(item.query.toLowerCase() === query.toLowerCase() && item.searchType === searchType)
            );

            // Add new entry at the beginning
            history.unshift(searchEntry);

            // Limit the number of stored items
            if (history.length > this.maxHistoryItems) {
                history = history.slice(0, this.maxHistoryItems);
            }

            localStorage.setItem(this.storageKey, JSON.stringify(history));
            console.log('💾 Search saved to history:', searchEntry);

        } catch (error) {
            console.error('Failed to save search to history:', error);
        }
    }

    /**
     * Get search history from local storage
     * @returns {Array} Array of search history entries
     */
    getSearchHistory() {
        try {
            const history = localStorage.getItem(this.storageKey);
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('Failed to load search history:', error);
            return [];
        }
    }

    /**
     * Get recent searches for display
     * @param {number} limit - Maximum number of items to return
     * @returns {Array} Array of recent search entries
     */
    getRecentSearches(limit = this.maxDisplayItems) {
        const history = this.getSearchHistory();
        return history.slice(0, limit);
    }

    /**
     * Get search suggestions based on partial query
     * @param {string} partialQuery - Partial search query
     * @param {string} searchType - Current search type
     * @returns {Array} Array of matching search suggestions
     */
    getSearchSuggestions(partialQuery, searchType = null) {
        if (!partialQuery || partialQuery.length < 2) {
            return this.getRecentSearches(5);
        }

        const history = this.getSearchHistory();
        const query = partialQuery.toLowerCase();

        const suggestions = history.filter(item => {
            const matchesQuery = item.query.toLowerCase().includes(query);
            const matchesType = !searchType || item.searchType === searchType;
            return matchesQuery && matchesType;
        });

        return suggestions.slice(0, this.maxDisplayItems);
    }

    /**
     * Clear all search history
     */
    clearHistory() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('🗑️ Search history cleared');
        } catch (error) {
            console.error('Failed to clear search history:', error);
        }
    }

    /**
     * Remove a specific search entry
     * @param {string} id - ID of the search entry to remove
     */
    removeSearchEntry(id) {
        try {
            let history = this.getSearchHistory();
            history = history.filter(item => item.id !== id);
            localStorage.setItem(this.storageKey, JSON.stringify(history));
            console.log('🗑️ Search entry removed:', id);
        } catch (error) {
            console.error('Failed to remove search entry:', error);
        }
    }

    /**
     * Get search statistics
     * @returns {Object} Statistics about search history
     */
    getSearchStats() {
        const history = this.getSearchHistory();
        const stats = {
            totalSearches: history.length,
            searchTypes: {},
            popularQueries: {},
            recentActivity: []
        };

        // Count search types
        history.forEach(item => {
            stats.searchTypes[item.searchType] = (stats.searchTypes[item.searchType] || 0) + 1;
            stats.popularQueries[item.query] = (stats.popularQueries[item.query] || 0) + 1;
        });

        // Get recent activity (last 7 days)
        const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        stats.recentActivity = history.filter(item => item.timestamp > weekAgo);

        return stats;
    }

    /**
     * Export search history as JSON
     * @returns {string} JSON string of search history
     */
    exportHistory() {
        const history = this.getSearchHistory();
        return JSON.stringify(history, null, 2);
    }

    /**
     * Import search history from JSON
     * @param {string} jsonData - JSON string of search history
     */
    importHistory(jsonData) {
        try {
            const importedHistory = JSON.parse(jsonData);
            if (Array.isArray(importedHistory)) {
                localStorage.setItem(this.storageKey, JSON.stringify(importedHistory));
                console.log('📥 Search history imported');
            }
        } catch (error) {
            console.error('Failed to import search history:', error);
        }
    }

    /**
     * Clean up old entries (older than 30 days)
     */
    cleanupOldEntries() {
        try {
            const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
            let history = this.getSearchHistory();
            const originalLength = history.length;
            
            history = history.filter(item => item.timestamp > thirtyDaysAgo);
            
            if (history.length !== originalLength) {
                localStorage.setItem(this.storageKey, JSON.stringify(history));
                console.log(`🧹 Cleaned up ${originalLength - history.length} old search entries`);
            }
        } catch (error) {
            console.error('Failed to cleanup old entries:', error);
        }
    }

    /**
     * Generate a unique ID for search entries
     * @returns {string} Unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Listen for search form submissions
        document.addEventListener('htmx:afterRequest', (event) => {
            if (event.detail.target && event.detail.target.id === 'recipe-results') {
                this.handleSearchSubmission(event);
            }
        });

        // Listen for successful search results
        document.addEventListener('htmx:afterSwap', (event) => {
            if (event.detail.target && event.detail.target.id === 'recipe-results') {
                this.updateSearchWithResults(event);
            }
        });
    }

    /**
     * Handle search form submission
     * @param {Event} event - HTMX event
     */
    handleSearchSubmission(event) {
        try {
            const form = document.querySelector('#main-search-box form');
            if (!form) return;

            const formData = new FormData(form);
            const query = formData.get('search_query');
            const searchType = formData.get('search_type');
            const cuisine = formData.get('cuisine');
            const quickFilter = formData.get('quick_filter');

            if (query && searchType) {
                const filters = {};
                if (cuisine) filters.cuisine = cuisine;
                if (quickFilter) filters.quickFilter = quickFilter;

                this.saveSearch(query, searchType, filters);
            }
        } catch (error) {
            console.error('Failed to handle search submission:', error);
        }
    }

    /**
     * Update search entry with result count
     * @param {Event} event - HTMX event
     */
    updateSearchWithResults(event) {
        try {
            // Count the number of recipe results
            const resultsContainer = event.detail.target;
            const recipeCards = resultsContainer.querySelectorAll('.recipe-card');
            const resultCount = recipeCards.length;

            // Update the most recent search entry with result count
            const history = this.getSearchHistory();
            if (history.length > 0) {
                history[0].resultCount = resultCount;
                localStorage.setItem(this.storageKey, JSON.stringify(history));
            }
        } catch (error) {
            console.error('Failed to update search with results:', error);
        }
    }
}

// Initialize the search history manager (prevent redeclaration)
window.searchHistoryManager = window.searchHistoryManager || new SearchHistoryManager();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchHistoryManager;
}
